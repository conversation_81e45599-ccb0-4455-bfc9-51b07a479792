use std::fs;
use serde_json;

/// 测试有效的JSON解析
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 测试有效JSON解析");
    
    // 读取测试JSON文件
    let json_content = fs::read_to_string("test_sample.json")?;
    println!("📄 JSON文件大小: {} 字节", json_content.len());
    
    // 尝试解析
    match serde_json::from_str::<serde_json::Value>(&json_content) {
        Ok(parsed) => {
            println!("✅ JSON解析成功！");
            
            if let Some(title) = parsed.get("translated_title") {
                println!("📝 标题: {}", title.as_str().unwrap_or("N/A"));
            }
            
            if let Some(summary) = parsed.get("summary") {
                println!("📋 概要: {}", summary.as_str().unwrap_or("N/A"));
            }
            
            if let Some(body) = parsed.get("translated_body") {
                let body_str = body.as_str().unwrap_or("N/A");
                let preview = if body_str.len() > 100 {
                    // 安全截断
                    let mut end = 100;
                    while end > 0 && !body_str.is_char_boundary(end) {
                        end -= 1;
                    }
                    format!("{}...", &body_str[..end])
                } else {
                    body_str.to_string()
                };
                println!("📄 内容预览: {}", preview);
            }
            
            println!("🎉 JSON解析功能正常工作！");
        }
        Err(e) => {
            println!("❌ JSON解析失败: {}", e);
        }
    }
    
    Ok(())
}
