use std::fs;
use serde_json;

/// 测试JSON解析功能
/// 这个文件用于测试从error.log中提取的JSON数据是否能被正确解析
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 开始测试JSON解析功能");
    println!("📄 读取error.log文件中的JSON数据");
    
    // 读取error.log文件
    let error_log_content = match fs::read_to_string("error.log") {
        Ok(content) => content,
        Err(e) => {
            eprintln!("❌ 无法读取error.log文件: {}", e);
            return Err(e.into());
        }
    };
    
    println!("✅ 成功读取error.log文件，大小: {} 字节", error_log_content.len());
    
    // 从error.log中提取JSON数据
    let json_blocks = extract_json_blocks(&error_log_content);
    println!("🔍 找到 {} 个JSON数据块", json_blocks.len());
    
    // 测试每个JSON块
    for (i, json_block) in json_blocks.iter().enumerate() {
        println!("\n📋 测试JSON块 #{}", i + 1);
        println!("📏 原始长度: {} 字符", json_block.len());
        
        // 显示原始JSON的前100个字符（安全截断）
        let preview = if json_block.len() > 100 {
            let safe_end = safe_truncate_at(json_block, 100);
            format!("{}...", &json_block[..safe_end])
        } else {
            json_block.clone()
        };
        println!("👀 原始预览: {}", preview);
        
        // 测试原始JSON解析
        test_json_parsing("原始JSON", json_block);
        
        // 测试清理后的JSON解析
        let cleaned_json = clean_json_response(json_block);
        println!("🧹 清理后长度: {} 字符", cleaned_json.len());
        test_json_parsing("清理后JSON", &cleaned_json);
        
        // 测试超级清理后的JSON解析
        let ultra_cleaned_json = ultra_clean_json(&cleaned_json);
        println!("🔧 超级清理后长度: {} 字符", ultra_cleaned_json.len());
        test_json_parsing("超级清理后JSON", &ultra_cleaned_json);

        // 尝试手动修复JSON
        let manually_fixed_json = manually_fix_json(&ultra_cleaned_json);
        println!("🔨 手动修复后长度: {} 字符", manually_fixed_json.len());
        test_json_parsing("手动修复后JSON", &manually_fixed_json);
        
        println!("{}", "=".repeat(60));
    }
    
    println!("🎉 JSON解析测试完成！");
    Ok(())
}

/// 从error.log内容中提取JSON数据块
fn extract_json_blocks(content: &str) -> Vec<String> {
    let mut json_blocks = Vec::new();
    let lines: Vec<&str> = content.lines().collect();

    let mut i = 0;
    while i < lines.len() {
        let line = lines[i].trim();

        // 查找包含"Raw response:"的行
        if line.contains("Raw response:") {
            // 检查这一行是否已经包含JSON开始
            if line.contains("{") {
                // 从这一行的JSON部分开始
                let json_start = line.find("{").unwrap();
                let first_part = &line[json_start..];

                let mut json_lines = vec![first_part];
                let mut brace_count = 0;

                // 计算第一行的大括号
                for ch in first_part.chars() {
                    match ch {
                        '{' => brace_count += 1,
                        '}' => brace_count -= 1,
                        _ => {}
                    }
                }

                // 如果第一行就完整了，直接返回
                if brace_count == 0 {
                    json_blocks.push(first_part.to_string());
                    i += 1;
                    continue;
                }

                // 否则继续收集后续行
                i += 1;
                while i < lines.len() && brace_count > 0 {
                    let json_line = lines[i];
                    json_lines.push(json_line);

                    // 计算大括号数量
                    for ch in json_line.chars() {
                        match ch {
                            '{' => brace_count += 1,
                            '}' => brace_count -= 1,
                            _ => {}
                        }
                    }

                    i += 1;
                }

                if !json_lines.is_empty() {
                    let json_block = json_lines.join("\n");
                    json_blocks.push(json_block);
                }
            } else {
                // 从下一行开始收集JSON内容
                i += 1;
                let mut json_lines = Vec::new();
                let mut brace_count = 0;
                let mut started = false;

                while i < lines.len() {
                    let json_line = lines[i];

                    // 计算大括号数量来确定JSON结束位置
                    for ch in json_line.chars() {
                        match ch {
                            '{' => {
                                brace_count += 1;
                                started = true;
                            }
                            '}' => {
                                brace_count -= 1;
                            }
                            _ => {}
                        }
                    }

                    if started {
                        json_lines.push(json_line);
                    }

                    // 如果大括号平衡且已开始，说明JSON结束
                    if started && brace_count == 0 {
                        break;
                    }

                    i += 1;
                }

                if !json_lines.is_empty() {
                    let json_block = json_lines.join("\n");
                    json_blocks.push(json_block);
                }
            }
        }

        i += 1;
    }

    json_blocks
}

/// 安全地截断字符串，避免UTF-8字符边界问题
fn safe_truncate_at(s: &str, max_bytes: usize) -> usize {
    if s.len() <= max_bytes {
        return s.len();
    }

    // 从目标位置向前查找，找到有效的UTF-8字符边界
    let mut end = max_bytes;
    while end > 0 && !s.is_char_boundary(end) {
        end -= 1;
    }

    end
}

/// 测试JSON解析
fn test_json_parsing(test_name: &str, json_str: &str) {
    println!("🔍 测试 {}", test_name);
    
    // 分析控制字符
    analyze_control_characters(json_str);
    
    // 尝试解析JSON
    match serde_json::from_str::<serde_json::Value>(json_str) {
        Ok(parsed) => {
            println!("✅ {} 解析成功！", test_name);
            
            // 检查是否包含预期的字段
            if let Some(title) = parsed.get("translated_title") {
                println!("📝 标题: {}", title.as_str().unwrap_or("N/A"));
            }
            if let Some(summary) = parsed.get("summary") {
                println!("📋 概要: {}", summary.as_str().unwrap_or("N/A"));
            }
        }
        Err(e) => {
            println!("❌ {} 解析失败: {}", test_name, e);
            
            // 显示错误位置附近的内容
            let line = e.line();
            let column = e.column();
            println!("📍 错误位置: 第{}行，第{}列", line, column);
            show_error_context(json_str, line, column);
        }
    }
}

/// 分析字符串中的控制字符
fn analyze_control_characters(text: &str) {
    let mut control_chars = Vec::new();
    let mut line_num = 1;
    let mut col_num = 0;

    for (i, c) in text.chars().enumerate() {
        if c == '\n' {
            line_num += 1;
            col_num = 0;
        } else {
            col_num += 1;
        }

        if c.is_control() && c != '\n' && c != '\r' && c != '\t' {
            control_chars.push((i, c, c as u32, line_num, col_num));
        }
    }

    if !control_chars.is_empty() {
        println!("⚠️  发现 {} 个控制字符:", control_chars.len());
        for (pos, _ch, code, line, col) in control_chars.iter().take(10) {
            println!("   位置 {} (第{}行第{}列): U+{:04X}", pos, line, col, code);
        }
        if control_chars.len() > 10 {
            println!("   ... 还有 {} 个控制字符", control_chars.len() - 10);
        }

        // 显示第4行的详细信息（因为错误总是在第4行）
        let lines: Vec<&str> = text.lines().collect();
        if lines.len() >= 4 {
            println!("🔍 第4行详细分析:");
            let line4 = lines[3];
            println!("   内容: {}", line4);
            println!("   长度: {} 字符", line4.len());

            // 分析第4行的每个字符
            for (i, c) in line4.chars().enumerate() {
                if c.is_control() {
                    println!("   第{}个字符是控制字符: U+{:04X}", i, c as u32);
                }
            }
        }
    } else {
        println!("✅ 未发现异常控制字符");
    }
}

/// 显示错误位置附近的内容
fn show_error_context(text: &str, error_line: usize, error_column: usize) {
    let lines: Vec<&str> = text.lines().collect();
    
    if error_line > 0 && error_line <= lines.len() {
        let line_index = error_line - 1; // 转换为0基索引
        let line = lines[line_index];
        
        println!("📄 错误行内容: {}", line);
        
        if error_column > 0 && error_column <= line.len() {
            let char_index = error_column - 1;
            if let Some(error_char) = line.chars().nth(char_index) {
                println!("🎯 错误字符: '{}' (U+{:04X})", error_char, error_char as u32);
            }
        }
    }
}

/// 清理JSON响应中的markdown格式和控制字符
/// 这个函数复制自ai_translator.rs中的实现
fn clean_json_response(response_text: &str) -> String {
    let mut cleaned = response_text.trim().to_string();

    // 移除markdown代码块标记
    if cleaned.starts_with("```json") {
        cleaned = cleaned.replace("```json", "").replace("```", "").trim().to_string();
    } else if cleaned.starts_with("```") {
        cleaned = cleaned.replace("```", "").trim().to_string();
    }

    // 更彻底地清理控制字符和不可见字符
    cleaned = cleaned
        .chars()
        .filter_map(|c| {
            match c {
                // 保留基本的JSON结构字符
                '{' | '}' | '[' | ']' | '"' | ':' | ',' | ' ' => Some(c),
                // 保留换行符和制表符用于格式化
                '\n' | '\r' | '\t' => Some(c),
                // 保留所有可打印的ASCII字符
                c if c.is_ascii() && !c.is_control() => Some(c),
                // 保留非ASCII的可打印字符（如中文）
                c if !c.is_ascii() && !c.is_control() => Some(c),
                // 移除所有其他控制字符
                _ => None,
            }
        })
        .collect();

    // 额外的清理步骤：移除可能的BOM和其他不可见字符
    if cleaned.starts_with('\u{FEFF}') {
        cleaned = cleaned.trim_start_matches('\u{FEFF}').to_string();
    }

    // 确保JSON格式正确：去除多余的空白行
    let lines: Vec<&str> = cleaned.lines().collect();
    let filtered_lines: Vec<&str> = lines.into_iter()
        .filter(|line| !line.trim().is_empty() || line.contains('"'))
        .collect();

    filtered_lines.join("\n")
}

/// 超级激进的JSON清理方法
/// 这个函数复制自ai_translator.rs中的实现
fn ultra_clean_json(text: &str) -> String {
    // 只保留JSON必需的字符和中文字符
    let ultra_cleaned: String = text
        .chars()
        .filter(|&c| {
            match c {
                // JSON结构字符
                '{' | '}' | '[' | ']' | '"' | ':' | ',' => true,
                // 空格和基本空白字符
                ' ' | '\n' | '\t' => true,
                // 数字和基本ASCII字母
                '0'..='9' | 'a'..='z' | 'A'..='Z' => true,
                // 常见标点符号
                '.' | '-' | '_' | '(' | ')' | '/' | '\\' | '=' | '+' | '%' | '&' | '!' | '?' | ';' => true,
                // 中文字符范围（简化检查）
                c if c as u32 >= 0x4E00 && c as u32 <= 0x9FFF => true,
                // 其他常见Unicode字符
                c if c as u32 >= 0x3000 && c as u32 <= 0x303F => true, // CJK符号
                c if c as u32 >= 0xFF00 && c as u32 <= 0xFFEF => true, // 全角字符
                _ => false,
            }
        })
        .collect();

    // 移除多余的空白行，但保留JSON结构
    let lines: Vec<&str> = ultra_cleaned.lines().collect();
    let mut result_lines = Vec::new();

    for line in lines {
        let trimmed = line.trim();
        if !trimmed.is_empty() {
            result_lines.push(line);
        }
    }

    result_lines.join("\n")
}

/// 手动修复JSON中的常见问题
fn manually_fix_json(text: &str) -> String {
    let mut fixed = text.to_string();

    // 替换可能有问题的字符
    fixed = fixed.replace('\u{00A0}', " "); // 非断行空格
    fixed = fixed.replace('\u{2028}', "\\n"); // 行分隔符
    fixed = fixed.replace('\u{2029}', "\\n"); // 段落分隔符

    // 移除所有控制字符（除了基本的空白字符）
    fixed = fixed.chars()
        .filter(|&c| {
            match c {
                '\n' | '\r' | '\t' | ' ' => true,
                c if c.is_control() => false,
                _ => true,
            }
        })
        .collect();

    // 修复可能的换行问题
    fixed = fixed.replace("\r\n", "\n");
    fixed = fixed.replace("\r", "\n");

    // 确保JSON字符串内的换行符被正确转义
    let lines: Vec<&str> = fixed.lines().collect();
    let mut result_lines = Vec::new();
    let mut in_string = false;
    let mut escape_next = false;

    for line in lines {
        let mut new_line = String::new();

        for c in line.chars() {
            if escape_next {
                new_line.push(c);
                escape_next = false;
            } else if c == '\\' {
                new_line.push(c);
                escape_next = true;
            } else if c == '"' {
                new_line.push(c);
                in_string = !in_string;
            } else {
                new_line.push(c);
            }
        }

        result_lines.push(new_line);
    }

    result_lines.join("\n")
}
