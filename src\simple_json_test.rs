use std::fs;
use serde_json;

/// 简单的JSON测试工具
/// 直接从error.log中提取第一个JSON并进行详细分析
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 简单JSON测试开始");
    
    // 读取error.log文件
    let content = fs::read_to_string("error.log")?;
    
    // 手动提取第一个JSON（从第3行开始到第26行结束）
    let lines: Vec<&str> = content.lines().collect();
    
    // 找到第一个JSON块（从"❌ Raw response: {"开始）
    let mut json_lines = Vec::new();
    let mut found_start = false;
    
    for line in lines {
        if line.contains("❌ Raw response: {") {
            // 提取这一行中的JSON部分
            if let Some(start_pos) = line.find("{") {
                json_lines.push(&line[start_pos..]);
                found_start = true;
            }
        } else if found_start {
            if line.trim().is_empty() || line.starts_with("❌") {
                // 遇到空行或下一个错误消息，停止
                break;
            }
            json_lines.push(line);
        }
    }
    
    if json_lines.is_empty() {
        println!("❌ 未找到JSON数据");
        return Ok(());
    }
    
    let json_text = json_lines.join("\n");
    println!("📄 提取的JSON长度: {} 字符", json_text.len());
    println!("📄 JSON行数: {}", json_lines.len());
    
    // 保存原始JSON到文件
    fs::write("extracted_json.json", &json_text)?;
    println!("💾 原始JSON已保存到 extracted_json.json");
    
    // 显示前几行
    println!("\n📋 JSON前5行:");
    for (i, line) in json_lines.iter().take(5).enumerate() {
        println!("  {}: {}", i + 1, line);
    }
    
    // 字符级分析
    println!("\n🔍 字符级分析:");
    analyze_characters(&json_text);
    
    // 尝试解析
    println!("\n🧪 尝试解析原始JSON:");
    test_parse(&json_text, "原始");
    
    // 清理并再次尝试
    let cleaned = clean_json(&json_text);
    fs::write("cleaned_json.json", &cleaned)?;
    println!("💾 清理后JSON已保存到 cleaned_json.json");
    
    println!("\n🧪 尝试解析清理后JSON:");
    test_parse(&cleaned, "清理后");
    
    Ok(())
}

/// 分析字符
fn analyze_characters(text: &str) {
    let mut control_count = 0;
    let mut non_ascii_count = 0;
    
    for (i, c) in text.chars().enumerate() {
        if c.is_control() && c != '\n' && c != '\r' && c != '\t' {
            control_count += 1;
            if control_count <= 5 {
                println!("  控制字符 #{}: 位置 {}, U+{:04X}", control_count, i, c as u32);
            }
        }
        
        if !c.is_ascii() {
            non_ascii_count += 1;
        }
    }
    
    println!("📊 统计:");
    println!("  总字符数: {}", text.chars().count());
    println!("  控制字符数: {}", control_count);
    println!("  非ASCII字符数: {}", non_ascii_count);
}

/// 测试JSON解析
fn test_parse(json_text: &str, name: &str) {
    match serde_json::from_str::<serde_json::Value>(json_text) {
        Ok(parsed) => {
            println!("✅ {} JSON解析成功!", name);
            if let Some(title) = parsed.get("translated_title") {
                println!("  标题: {}", title.as_str().unwrap_or("N/A"));
            }
        }
        Err(e) => {
            println!("❌ {} JSON解析失败: {}", name, e);
            if let Some(line) = e.line().checked_sub(1) {
                let lines: Vec<&str> = json_text.lines().collect();
                if line < lines.len() {
                    println!("  错误行: {}", lines[line]);
                    
                    // 分析错误行的字符
                    println!("  错误行字符分析:");
                    for (i, c) in lines[line].chars().enumerate() {
                        if c.is_control() {
                            println!("    位置 {}: U+{:04X} (控制字符)", i, c as u32);
                        }
                    }
                }
            }
        }
    }
}

/// 清理JSON
fn clean_json(text: &str) -> String {
    text.chars()
        .filter(|&c| {
            match c {
                // 保留JSON结构字符
                '{' | '}' | '[' | ']' | '"' | ':' | ',' | ' ' | '\n' | '\t' => true,
                // 保留可打印ASCII字符
                c if c.is_ascii() && !c.is_control() => true,
                // 保留中文等非ASCII字符
                c if !c.is_ascii() && !c.is_control() => true,
                // 移除控制字符
                _ => false,
            }
        })
        .collect()
}
