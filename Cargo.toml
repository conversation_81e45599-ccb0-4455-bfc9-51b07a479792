[package]
name = "BinanceWebsocketNotifier"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "test_json_parser"
path = "src/test_json_parser.rs"

[[bin]]
name = "simple_json_test"
path = "src/simple_json_test.rs"

[[bin]]
name = "test_valid_json"
path = "src/test_valid_json.rs"

[[bin]]
name = "improved_json_parser"
path = "src/improved_json_parser.rs"

[dependencies]
chrono = { version = "0.4.41", features = ["serde"] }
dotenvy = "0.15.7"
env_logger = "0.11.8"
futures-util = "0.3.31"
hex = "0.4.3"
hmac = "0.12.1"
reqwest = { version = "0.12.22", features = ["json"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.141"
sha2 = "0.10.9"
thiserror = "2.0.12"
tokio = { version = "1.47.0", features = ["full", "signal"] }
tokio-tungstenite = { version = "0.27.0", features = ["native-tls"] }
url = "2.5.4"


