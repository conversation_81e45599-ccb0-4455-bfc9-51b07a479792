use serde_json;

/// 改进的JSON解析器，专门处理AI返回的可能有问题的JSON
pub struct ImprovedJsonParser;

impl ImprovedJsonParser {
    /// 解析AI返回的JSON响应
    pub fn parse_ai_response(response_text: &str) -> Result<serde_json::Value, String> {
        println!("🔍 开始解析AI响应，长度: {} 字符", response_text.len());
        
        // 第一步：基本清理
        let cleaned = Self::basic_clean(response_text);
        println!("🧹 基本清理后长度: {} 字符", cleaned.len());
        
        // 尝试解析基本清理后的JSON
        if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(&cleaned) {
            println!("✅ 基本清理后解析成功");
            return Ok(parsed);
        }
        
        // 第二步：修复字符串中的换行符
        let fixed_newlines = Self::fix_json_newlines(&cleaned);
        println!("🔧 修复换行符后长度: {} 字符", fixed_newlines.len());
        
        // 尝试解析修复换行符后的JSON
        if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(&fixed_newlines) {
            println!("✅ 修复换行符后解析成功");
            return Ok(parsed);
        }
        
        // 第三步：激进清理
        let ultra_cleaned = Self::ultra_clean(&fixed_newlines);
        println!("🔨 激进清理后长度: {} 字符", ultra_cleaned.len());
        
        // 尝试解析激进清理后的JSON
        if let Ok(parsed) = serde_json::from_str::<serde_json::Value>(&ultra_cleaned) {
            println!("✅ 激进清理后解析成功");
            return Ok(parsed);
        }
        
        // 第四步：尝试修复常见的JSON格式问题
        let format_fixed = Self::fix_json_format(&ultra_cleaned);
        println!("🛠️ 格式修复后长度: {} 字符", format_fixed.len());
        
        // 最后尝试
        match serde_json::from_str::<serde_json::Value>(&format_fixed) {
            Ok(parsed) => {
                println!("✅ 格式修复后解析成功");
                Ok(parsed)
            }
            Err(e) => {
                println!("❌ 所有尝试都失败了");
                println!("最终错误: {}", e);
                println!("最终JSON内容: {}", format_fixed);
                Err(format!("JSON解析失败: {}", e))
            }
        }
    }
    
    /// 基本清理：移除markdown标记和BOM
    fn basic_clean(text: &str) -> String {
        let mut cleaned = text.trim().to_string();
        
        // 移除markdown代码块标记
        if cleaned.starts_with("```json") {
            cleaned = cleaned.replace("```json", "").replace("```", "").trim().to_string();
        } else if cleaned.starts_with("```") {
            cleaned = cleaned.replace("```", "").trim().to_string();
        }
        
        // 移除BOM
        if cleaned.starts_with('\u{FEFF}') {
            cleaned = cleaned.trim_start_matches('\u{FEFF}').to_string();
        }
        
        cleaned
    }
    
    /// 修复JSON字符串中的换行符问题
    fn fix_json_newlines(text: &str) -> String {
        let mut result = String::new();
        let mut in_string = false;
        let mut escape_next = false;
        let mut chars = text.chars().peekable();
        
        while let Some(c) = chars.next() {
            if escape_next {
                result.push(c);
                escape_next = false;
                continue;
            }
            
            match c {
                '\\' => {
                    result.push(c);
                    escape_next = true;
                }
                '"' => {
                    result.push(c);
                    in_string = !in_string;
                }
                '\n' if in_string => {
                    // 在字符串内的换行符需要转义
                    result.push_str("\\n");
                }
                '\r' if in_string => {
                    // 在字符串内的回车符需要转义
                    result.push_str("\\r");
                }
                '\t' if in_string => {
                    // 在字符串内的制表符需要转义
                    result.push_str("\\t");
                }
                _ => {
                    result.push(c);
                }
            }
        }
        
        result
    }
    
    /// 激进清理：移除所有控制字符
    fn ultra_clean(text: &str) -> String {
        text.chars()
            .filter(|&c| {
                match c {
                    // 保留JSON结构字符
                    '{' | '}' | '[' | ']' | '"' | ':' | ',' | ' ' => true,
                    // 保留基本空白字符
                    '\n' | '\t' => true,
                    // 保留可打印ASCII字符
                    c if c.is_ascii() && !c.is_control() => true,
                    // 保留中文等非ASCII字符
                    c if !c.is_ascii() && !c.is_control() => true,
                    // 移除其他控制字符
                    _ => false,
                }
            })
            .collect()
    }
    
    /// 修复常见的JSON格式问题
    fn fix_json_format(text: &str) -> String {
        let mut fixed = text.to_string();
        
        // 确保JSON对象正确闭合
        let open_braces = fixed.matches('{').count();
        let close_braces = fixed.matches('}').count();
        
        if open_braces > close_braces {
            for _ in 0..(open_braces - close_braces) {
                fixed.push('}');
            }
        }
        
        // 移除多余的逗号
        fixed = fixed.replace(",}", "}");
        fixed = fixed.replace(",]", "]");
        
        // 修复可能的双引号问题
        fixed = Self::fix_quotes(&fixed);
        
        fixed
    }
    
    /// 修复引号问题
    fn fix_quotes(text: &str) -> String {
        let mut result = String::new();
        let mut in_string = false;
        let mut escape_next = false;
        
        for c in text.chars() {
            if escape_next {
                result.push(c);
                escape_next = false;
                continue;
            }
            
            match c {
                '\\' => {
                    result.push(c);
                    escape_next = true;
                }
                '"' => {
                    result.push(c);
                    in_string = !in_string;
                }
                _ => {
                    result.push(c);
                }
            }
        }
        
        // 如果字符串没有正确闭合，添加闭合引号
        if in_string {
            result.push('"');
        }
        
        result
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_basic_json() {
        let json = r#"{"title": "test", "body": "content"}"#;
        let result = ImprovedJsonParser::parse_ai_response(json);
        assert!(result.is_ok());
    }
    
    #[test]
    fn test_json_with_newlines() {
        let json = r#"{"title": "test", "body": "line1
line2
line3"}"#;
        let result = ImprovedJsonParser::parse_ai_response(json);
        assert!(result.is_ok());
    }
    
    #[test]
    fn test_markdown_wrapped_json() {
        let json = r#"```json
{"title": "test", "body": "content"}
```"#;
        let result = ImprovedJsonParser::parse_ai_response(json);
        assert!(result.is_ok());
    }
}

fn main() {
    println!("🧪 改进的JSON解析器测试");
    
    // 测试从error.log中提取的有问题的JSON
    let problematic_json = r#"{
    "translated_title": "币安超级质押：SIGN代币上线，BNSOL持有者可获额外空投奖励",
    "translated_body": "币安很高兴宣布在BNSOL超级质押平台推出第12个项目 - SIGN（SIGN），这是一个全链证明协议，为政府提供数字公共基础设施，并作为去中心化应用的基础层，专注于身份验证、所有权证明和合约验证。

从2025年8月5日00:00（UTC）至2025年9月5日23:59（UTC），满足以下条件的用户将获得SIGN APR加速空投奖励：

1. 币安账户：持有BNSOL或质押SOL到BNSOL
2. 币安钱包：持有以下DeFi BNSOL资产：
   - 币安质押SOL (BNSOL)
   - Solayer币安SOL (sBNSOL)
   - Renzo重质押BNSOL (bzSOL)

这些SIGN APR加速空投奖励将在币安SOL质押基础收益之上额外提供奖励，为用户带来更高的年化收益。",
    "summary": "币安宣布SIGN代币上线BNSOL超级质押平台，即日起至2025年9月5日，持有BNSOL或特定DeFi BNSOL资产的用户可获得总计2,481,081.75 SIGN的额外空投奖励。"
}"#;
    
    match ImprovedJsonParser::parse_ai_response(problematic_json) {
        Ok(parsed) => {
            println!("✅ 解析成功！");
            if let Some(title) = parsed.get("translated_title") {
                println!("📝 标题: {}", title.as_str().unwrap_or("N/A"));
            }
        }
        Err(e) => {
            println!("❌ 解析失败: {}", e);
        }
    }
}
